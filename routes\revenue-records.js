const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { verifyToken, checkAdminRole } = require('../middleware/auth');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const ExcelJS = require('exceljs');
const sharp = require('sharp');

// 创建收入记录专用店铺表（如果不存在）
async function createRevenueShopsTable() {
    try {
        await db.query(`
            CREATE TABLE IF NOT EXISTS revenue_shops (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL UNIQUE,
                address VARCHAR(500),
                contact VARCHAR(255),
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('收入记录店铺表创建成功');
    } catch (error) {
        console.error('创建收入记录店铺表失败:', error);
    }
}

// 初始化表
createRevenueShopsTable();

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, '../public/uploads/revenue');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif|webp/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件'));
        }
    }
});

// 获取收入记录列表
router.get('/', verifyToken, async (req, res) => {
    try {
        const { page = 1, limit = 10, search, shop_name, start_date, end_date } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = '';
        let queryParams = [];

        // 权限控制：普通用户只能查看自己的数据，管理员可以查看所有数据
        if (req.user.role !== 'admin') {
            whereClause = 'WHERE r.user_id = ?';
            queryParams.push(req.user.id);
        } else {
            whereClause = 'WHERE 1=1';
        }

        // 添加搜索条件（搜索店铺名称、订单号、产品信息、创收备注）
        if (search && search.trim()) {
            whereClause += ' AND (r.shop_name LIKE ? OR r.order_number LIKE ? OR r.product_info LIKE ? OR r.revenue_notes LIKE ?)';
            const searchPattern = `%${search.trim()}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
        }

        // 添加筛选条件
        if (shop_name) {
            whereClause += ' AND r.shop_name LIKE ?';
            queryParams.push(`%${shop_name}%`);
        }

        if (start_date) {
            whereClause += ' AND DATE(r.created_at) >= ?';
            queryParams.push(start_date);
        }

        if (end_date) {
            whereClause += ' AND DATE(r.created_at) <= ?';
            queryParams.push(end_date);
        }
        
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM revenue_records r ${whereClause}`;
        const [countResult] = await db.query(countQuery, queryParams);
        const total = countResult[0].total;

        // 获取数据
        const dataQuery = `
            SELECT r.*, u.username as creator_name
            FROM revenue_records r
            LEFT JOIN users u ON r.user_id = u.id
            ${whereClause}
            ORDER BY r.created_at DESC
            LIMIT ? OFFSET ?
        `;
        queryParams.push(parseInt(limit), parseInt(offset));
        
        const [records] = await db.query(dataQuery, queryParams);
        
        res.json({
            success: true,
            data: records,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: total,
                totalPages: Math.ceil(total / limit)
            }
        });
        
    } catch (error) {
        console.error('获取收入记录失败:', error);
        res.status(500).json({ success: false, message: '获取收入记录失败' });
    }
});

// 获取统计数据
router.get('/statistics', verifyToken, async (req, res) => {
    try {
        let whereClause = '';
        let queryParams = [];
        
        // 权限控制
        if (req.user.role !== 'admin') {
            whereClause = 'WHERE user_id = ?';
            queryParams.push(req.user.id);
        } else {
            whereClause = 'WHERE 1=1';
        }
        
        // 总收入
        const [totalRevenueResult] = await db.query(
            `SELECT COALESCE(SUM(total_revenue), 0) as total FROM revenue_records ${whereClause}`,
            queryParams
        );
        
        // 订单数量
        const [totalOrdersResult] = await db.query(
            `SELECT COUNT(*) as total FROM revenue_records ${whereClause}`,
            queryParams
        );
        
        // 店铺数量
        const [totalShopsResult] = await db.query(
            `SELECT COUNT(DISTINCT shop_name) as total FROM revenue_records ${whereClause}`,
            queryParams
        );
        
        // 平均订单价值
        const [avgOrderValueResult] = await db.query(
            `SELECT COALESCE(AVG(total_revenue), 0) as avg FROM revenue_records ${whereClause}`,
            queryParams
        );
        
        res.json({
            success: true,
            data: {
                totalRevenue: totalRevenueResult[0].total,
                totalOrders: totalOrdersResult[0].total,
                totalShops: totalShopsResult[0].total,
                avgOrderValue: avgOrderValueResult[0].avg
            }
        });
        
    } catch (error) {
        console.error('获取统计数据失败:', error);
        res.status(500).json({ success: false, message: '获取统计数据失败' });
    }
});

// 获取图表数据
router.get('/charts', verifyToken, async (req, res) => {
    try {
        let whereClause = '';
        let queryParams = [];
        
        // 权限控制
        if (req.user.role !== 'admin') {
            whereClause = 'WHERE user_id = ?';
            queryParams.push(req.user.id);
        } else {
            whereClause = 'WHERE 1=1';
        }
        
        // 收入趋势数据（最近30天）
        const [trendData] = await db.query(`
            SELECT 
                DATE(created_at) as date,
                SUM(total_revenue) as revenue,
                COUNT(*) as orders
            FROM revenue_records 
            ${whereClause} AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        `, queryParams);
        
        // 店铺收入对比数据
        const [shopData] = await db.query(`
            SELECT
                shop_name,
                SUM(total_revenue) as revenue,
                COUNT(*) as orders
            FROM revenue_records
            ${whereClause}
            GROUP BY shop_name
            ORDER BY revenue DESC
            LIMIT 10
        `, queryParams);

        // 月度分析数据（最近12个月）
        const [monthlyData] = await db.query(`
            SELECT
                DATE_FORMAT(created_at, '%Y-%m') as month,
                SUM(total_revenue) as revenue,
                COUNT(*) as orders,
                AVG(total_revenue) as avg_revenue
            FROM revenue_records
            ${whereClause} AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        `, queryParams);

        // 年度分析数据（最近5年）
        const [yearlyData] = await db.query(`
            SELECT
                YEAR(created_at) as year,
                SUM(total_revenue) as revenue,
                COUNT(*) as orders,
                AVG(total_revenue) as avg_revenue
            FROM revenue_records
            ${whereClause} AND created_at >= DATE_SUB(NOW(), INTERVAL 5 YEAR)
            GROUP BY YEAR(created_at)
            ORDER BY year ASC
        `, queryParams);

        // 产品类别分析（基于产品名称关键词）
        const [categoryData] = await db.query(`
            SELECT
                CASE
                    WHEN product_name LIKE '%电脑%' OR product_name LIKE '%笔记本%' OR product_name LIKE '%台式%' THEN '电脑设备'
                    WHEN product_name LIKE '%手机%' OR product_name LIKE '%iPhone%' OR product_name LIKE '%华为%' OR product_name LIKE '%小米%' THEN '手机通讯'
                    WHEN product_name LIKE '%平板%' OR product_name LIKE '%iPad%' THEN '平板设备'
                    WHEN product_name LIKE '%耳机%' OR product_name LIKE '%音响%' OR product_name LIKE '%音箱%' THEN '音频设备'
                    WHEN product_name LIKE '%键盘%' OR product_name LIKE '%鼠标%' OR product_name LIKE '%显示器%' THEN '外设配件'
                    ELSE '其他产品'
                END as category,
                SUM(total_revenue) as revenue,
                COUNT(*) as orders
            FROM revenue_records
            ${whereClause}
            GROUP BY category
            ORDER BY revenue DESC
        `, queryParams);

        res.json({
            success: true,
            data: {
                trend: trendData,
                shops: shopData,
                monthly: monthlyData,
                yearly: yearlyData,
                categories: categoryData
            }
        });
        
    } catch (error) {
        console.error('获取图表数据失败:', error);
        res.status(500).json({ success: false, message: '获取图表数据失败' });
    }
});

// 获取店铺列表（用于筛选下拉框）
router.get('/shops', verifyToken, async (req, res) => {
    try {
        let whereClause = '';
        let queryParams = [];
        
        // 权限控制
        if (req.user.role !== 'admin') {
            whereClause = 'WHERE user_id = ?';
            queryParams.push(req.user.id);
        } else {
            whereClause = 'WHERE 1=1';
        }
        
        const [shops] = await db.query(`
            SELECT DISTINCT shop_name 
            FROM revenue_records 
            ${whereClause}
            ORDER BY shop_name ASC
        `, queryParams);
        
        res.json({
            success: true,
            data: shops.map(shop => shop.shop_name)
        });
        
    } catch (error) {
        console.error('获取店铺列表失败:', error);
        res.status(500).json({ success: false, message: '获取店铺列表失败' });
    }
});

// 数据验证函数
function validateRevenueRecord(data) {
    const errors = [];

    if (!data.shop_name || data.shop_name.trim().length === 0) {
        errors.push('店铺名称不能为空');
    } else if (data.shop_name.trim().length > 100) {
        errors.push('店铺名称不能超过100个字符');
    }

    if (!data.order_number || data.order_number.trim().length === 0) {
        errors.push('订单号不能为空');
    } else if (data.order_number.trim().length > 100) {
        errors.push('订单号不能超过100个字符');
    }

    if (!data.product_info || data.product_info.trim().length === 0) {
        errors.push('产品信息不能为空');
    }

    if (data.total_revenue === undefined || data.total_revenue === null || data.total_revenue === '' || isNaN(parseFloat(data.total_revenue))) {
        errors.push('创收金额必须是有效数字');
    } else if (parseFloat(data.total_revenue) < 0) {
        errors.push('创收金额不能为负数');
    } else if (parseFloat(data.total_revenue) > 999999.99) {
        errors.push('创收金额不能超过999999.99');
    }

    if (data.revenue_notes && data.revenue_notes.length > 1000) {
        errors.push('创收备注不能超过1000个字符');
    }

    return errors;
}

// 创建收入记录
router.post('/', verifyToken, (req, res, next) => {
    // 如果是JSON请求，跳过multer
    if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
        return next();
    }
    // 否则使用multer处理文件上传
    upload.single('image')(req, res, next);
}, async (req, res) => {
    try {
        const { shop_name, order_number, product_info, total_revenue, revenue_notes, status } = req.body;

        // 数据验证
        const validationErrors = validateRevenueRecord(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                success: false,
                message: '数据验证失败',
                errors: validationErrors
            });
        }

        // 检查订单号是否已存在
        const [existingOrder] = await db.query(
            'SELECT id FROM revenue_records WHERE order_number = ?',
            [order_number.trim()]
        );

        if (existingOrder.length > 0) {
            return res.status(400).json({
                success: false,
                message: `订单号 "${order_number.trim()}" 已存在，请使用不同的订单号`
            });
        }

        let image_url = null;
        if (req.file) {
            // 检查是否为图片类型并转换为WebP
            if (req.file.mimetype.startsWith('image/')) {
                try {
                    const originalPath = req.file.path;
                    const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                    const webpFilename = `${fileNameWithoutExt}.webp`;
                    const webpPath = path.join(path.dirname(originalPath), webpFilename);

                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);

                    // 删除原始文件
                    try {
                        fs.unlinkSync(originalPath);
                        console.log(`已删除原始文件: ${originalPath}`);
                    } catch (deleteErr) {
                        console.error('删除原始文件失败:', deleteErr);
                    }

                    // 更新图片URL为WebP格式
                    image_url = `/uploads/revenue/${webpFilename}`;
                    console.log(`创收记录图片已转换为WebP格式: ${webpFilename}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，保留原始文件
                    image_url = `/uploads/revenue/${req.file.filename}`;
                }
            } else {
                image_url = `/uploads/revenue/${req.file.filename}`;
            }
        }

        const [result] = await db.query(`
            INSERT INTO revenue_records
            (shop_name, order_number, product_info, image_url, revenue_notes, total_revenue, status, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [shop_name, order_number, product_info, image_url, revenue_notes, total_revenue, status || '未做单', req.user.id]);

        res.json({
            success: true,
            message: '收入记录创建成功',
            data: { id: result.insertId }
        });

    } catch (error) {
        console.error('创建收入记录失败:', error);
        res.status(500).json({ success: false, message: '创建收入记录失败' });
    }
});

// 更新收入记录
router.put('/:id', verifyToken, (req, res, next) => {
    // 如果是JSON请求，跳过multer
    if (req.headers['content-type'] && req.headers['content-type'].includes('application/json')) {
        return next();
    }
    // 否则使用multer处理文件上传
    upload.single('image')(req, res, next);
}, async (req, res) => {
    try {
        const { id } = req.params;
        const { shop_name, order_number, product_info, total_revenue, revenue_notes, status } = req.body;

        // 验证必填字段
        if (!shop_name || !order_number || !product_info || !total_revenue) {
            return res.status(400).json({ success: false, message: '请填写所有必填字段' });
        }

        // 检查记录是否存在且用户有权限修改
        let whereClause = 'WHERE id = ?';
        let queryParams = [id];

        if (req.user.role !== 'admin') {
            whereClause += ' AND user_id = ?';
            queryParams.push(req.user.id);
        }

        const [existingRecord] = await db.query(
            `SELECT * FROM revenue_records ${whereClause}`,
            queryParams
        );

        if (existingRecord.length === 0) {
            return res.status(404).json({ success: false, message: '记录不存在或无权限修改' });
        }

        // 检查订单号是否与其他记录冲突
        const [duplicateOrder] = await db.query(
            'SELECT id FROM revenue_records WHERE order_number = ? AND id != ?',
            [order_number, id]
        );

        if (duplicateOrder.length > 0) {
            return res.status(400).json({ success: false, message: '订单号已存在' });
        }

        let image_url = existingRecord[0].image_url;
        if (req.file) {
            // 删除旧图片
            if (image_url) {
                const oldImagePath = path.join(__dirname, '../public', image_url);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }

            // 检查是否为图片类型并转换为WebP
            if (req.file.mimetype.startsWith('image/')) {
                try {
                    const originalPath = req.file.path;
                    const fileNameWithoutExt = path.basename(req.file.filename, path.extname(req.file.filename));
                    const webpFilename = `${fileNameWithoutExt}.webp`;
                    const webpPath = path.join(path.dirname(originalPath), webpFilename);

                    // 使用sharp转换为WebP格式
                    await sharp(originalPath)
                        .webp({ quality: 100, lossless: true }) // 无损压缩，只转换格式
                        .toFile(webpPath);

                    // 删除原始文件
                    try {
                        fs.unlinkSync(originalPath);
                        console.log(`已删除原始文件: ${originalPath}`);
                    } catch (deleteErr) {
                        console.error('删除原始文件失败:', deleteErr);
                    }

                    // 更新图片URL为WebP格式
                    image_url = `/uploads/revenue/${webpFilename}`;
                    console.log(`创收记录图片已转换为WebP格式: ${webpFilename}`);
                } catch (err) {
                    console.error('WebP转换失败:', err);
                    // 如果转换失败，保留原始文件
                    image_url = `/uploads/revenue/${req.file.filename}`;
                }
            } else {
                image_url = `/uploads/revenue/${req.file.filename}`;
            }
        }

        await db.query(`
            UPDATE revenue_records
            SET shop_name = ?, order_number = ?, product_info = ?, image_url = ?,
                revenue_notes = ?, total_revenue = ?, status = ?, updated_at = NOW()
            WHERE id = ?
        `, [shop_name, order_number, product_info, image_url, revenue_notes, total_revenue, status || '未做单', id]);

        res.json({
            success: true,
            message: '收入记录更新成功'
        });

    } catch (error) {
        console.error('更新收入记录失败:', error);
        res.status(500).json({ success: false, message: '更新收入记录失败' });
    }
});

// 删除收入记录
router.delete('/:id', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;

        // 检查记录是否存在且用户有权限删除
        let whereClause = 'WHERE id = ?';
        let queryParams = [id];

        if (req.user.role !== 'admin') {
            whereClause += ' AND user_id = ?';
            queryParams.push(req.user.id);
        }

        const [existingRecord] = await db.query(
            `SELECT * FROM revenue_records ${whereClause}`,
            queryParams
        );

        if (existingRecord.length === 0) {
            return res.status(404).json({ success: false, message: '记录不存在或无权限删除' });
        }

        // 删除关联的图片文件
        if (existingRecord[0].image_url) {
            const imagePath = path.join(__dirname, '../public', existingRecord[0].image_url);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }
        }

        await db.query('DELETE FROM revenue_records WHERE id = ?', [id]);

        res.json({
            success: true,
            message: '收入记录删除成功'
        });

    } catch (error) {
        console.error('删除收入记录失败:', error);
        res.status(500).json({ success: false, message: '删除收入记录失败' });
    }
});

// 导出Excel
router.get('/export', verifyToken, async (req, res) => {
    try {
        const { shop_name, start_date, end_date } = req.query;

        let whereClause = '';
        let queryParams = [];

        // 权限控制
        if (req.user.role !== 'admin') {
            whereClause = 'WHERE user_id = ?';
            queryParams.push(req.user.id);
        } else {
            whereClause = 'WHERE 1=1';
        }

        // 添加筛选条件
        if (shop_name) {
            whereClause += ' AND shop_name LIKE ?';
            queryParams.push(`%${shop_name}%`);
        }

        if (start_date) {
            whereClause += ' AND DATE(r.created_at) >= ?';
            queryParams.push(start_date);
        }

        if (end_date) {
            whereClause += ' AND DATE(r.created_at) <= ?';
            queryParams.push(end_date);
        }

        const [records] = await db.query(`
            SELECT r.*, u.username as creator_name
            FROM revenue_records r
            LEFT JOIN users u ON r.user_id = u.id
            ${whereClause}
            ORDER BY r.id ASC
        `, queryParams);

        // 创建Excel工作簿
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('收入记录');

        // 设置列标题
        worksheet.columns = [
            { header: '店铺名称', key: 'shop_name', width: 15 },
            { header: '订单号', key: 'order_number', width: 20 },
            { header: '产品信息', key: 'product_info', width: 30 },
            { header: '创收金额', key: 'total_revenue', width: 12 },
            { header: '创收备注', key: 'revenue_notes', width: 25 },
            { header: '创建者', key: 'creator_name', width: 12 },
            { header: '创建时间', key: 'created_at', width: 20 },
            { header: '更新时间', key: 'updated_at', width: 20 }
        ];

        // 添加数据
        records.forEach(record => {
            worksheet.addRow({
                shop_name: record.shop_name,
                order_number: record.order_number,
                product_info: record.product_info,
                total_revenue: parseFloat(record.total_revenue),
                revenue_notes: record.revenue_notes || '',
                creator_name: record.creator_name,
                created_at: record.created_at,
                updated_at: record.updated_at
            });
        });

        // 设置标题行样式
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE0E0E0' }
        };

        // 设置响应头
        const filename = `收入记录_${new Date().toISOString().split('T')[0]}.xlsx`;
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);

        // 发送Excel文件
        await workbook.xlsx.write(res);
        res.end();

    } catch (error) {
        console.error('导出Excel失败:', error);
        res.status(500).json({ success: false, message: '导出Excel失败' });
    }
});

// 更新订单状态
router.put('/:id/status', verifyToken, async (req, res) => {
    console.log('=== 状态更新路由被调用 ===');
    try {
        const { id } = req.params;
        const { status } = req.body;

        console.log('收到状态更新请求:', { id, status, user: req.user?.id });

        // 验证状态值
        const validStatuses = ['未做单', '已做单', '已关闭'];
        if (!validStatuses.includes(status)) {
            console.log('无效的状态值:', status);
            return res.status(400).json({
                success: false,
                message: '无效的状态值，只能是"未做单"、"已做单"或"已关闭"'
            });
        }

        // 检查记录是否存在且用户有权限修改
        let whereClause = 'WHERE id = ?';
        let queryParams = [id];

        if (req.user.role !== 'admin') {
            whereClause += ' AND user_id = ?';
            queryParams.push(req.user.id);
        }

        const [existingRecord] = await db.query(
            `SELECT * FROM revenue_records ${whereClause}`,
            queryParams
        );

        if (existingRecord.length === 0) {
            return res.status(404).json({
                success: false,
                message: '记录不存在或无权限修改'
            });
        }

        // 更新状态
        await db.query(
            'UPDATE revenue_records SET status = ?, updated_at = NOW() WHERE id = ?',
            [status, id]
        );

        res.json({
            success: true,
            message: '订单状态更新成功'
        });

    } catch (error) {
        console.error('更新订单状态失败:', error);
        res.status(500).json({
            success: false,
            message: '更新订单状态失败'
        });
    }
});

// =======================================
// 收入记录专用店铺管理 API
// =======================================

// 获取所有收入记录店铺
router.get('/revenue-shops', verifyToken, async (req, res) => {
    try {
        const [shops] = await db.query('SELECT * FROM revenue_shops ORDER BY name');
        res.json(shops);
    } catch (error) {
        console.error('获取收入记录店铺列表失败:', error);
        res.status(500).json({ success: false, message: '获取店铺列表失败' });
    }
});

// 添加新的收入记录店铺
router.post('/revenue-shops', verifyToken, async (req, res) => {
    try {
        const { name, address = '', contact = '', notes = '' } = req.body;

        if (!name || name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '店铺名称不能为空'
            });
        }

        const [result] = await db.query(
            'INSERT INTO revenue_shops (name, address, contact, notes) VALUES (?, ?, ?, ?)',
            [name.trim(), address, contact, notes]
        );

        const newShop = {
            id: result.insertId,
            name: name.trim(),
            address,
            contact,
            notes
        };

        res.json({
            success: true,
            message: '店铺添加成功',
            data: newShop
        });

    } catch (error) {
        console.error('添加收入记录店铺失败:', error);

        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({
                success: false,
                message: '店铺名称已存在，请使用其他名称'
            });
        }

        res.status(500).json({
            success: false,
            message: '添加店铺失败'
        });
    }
});

// 更新收入记录店铺
router.put('/revenue-shops/:id', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { name, address = '', contact = '', notes = '' } = req.body;

        if (!name || name.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '店铺名称不能为空'
            });
        }

        const [result] = await db.query(
            'UPDATE revenue_shops SET name = ?, address = ?, contact = ?, notes = ?, updated_at = NOW() WHERE id = ?',
            [name.trim(), address, contact, notes, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '店铺不存在'
            });
        }

        res.json({
            success: true,
            message: '店铺更新成功'
        });

    } catch (error) {
        console.error('更新收入记录店铺失败:', error);

        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(400).json({
                success: false,
                message: '店铺名称已存在，请使用其他名称'
            });
        }

        res.status(500).json({
            success: false,
            message: '更新店铺失败'
        });
    }
});

// 删除收入记录店铺
router.delete('/revenue-shops/:id', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;

        const [result] = await db.query('DELETE FROM revenue_shops WHERE id = ?', [id]);

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: '店铺不存在'
            });
        }

        res.json({
            success: true,
            message: '店铺删除成功'
        });

    } catch (error) {
        console.error('删除收入记录店铺失败:', error);
        res.status(500).json({
            success: false,
            message: '删除店铺失败'
        });
    }
});

module.exports = router;
